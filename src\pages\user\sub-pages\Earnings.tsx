// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState, useEffect } from "react";
import * as echarts from "echarts";
import {
  DollarSign,
  Wallet,
  Search,
  ChevronDown,
  Download,
  FileText,
  FileIcon,
  FileSpreadsheet,
  RefreshCw,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  UserPlus,
  Users,
} from "lucide-react";
const Earnings: React.FC = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState("month");
  const [selectedChartPeriod, setSelectedChartPeriod] = useState("month");
  const [showExportOptions, setShowExportOptions] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("date");
  const [sortDirection, setSortDirection] = useState("desc");
  const [dateRange, setDateRange] = useState({ start: "", end: "" });
  const [transactionType, setTransactionType] = useState("all");
  const earningsSummary = {
    totalEarnings: 1875.5,
    totalEarningsChange: 12.4,
    totalDeposits: 5000.0,
    depositsChange: 8.5,
    availableBalance: 1629.75,
    availableBalanceChange: 15.7,
    usdtRate: 1.0,
    localCurrency: "USD",
    pendingEarningsChange: 10
  };
  const transactions = [
    {
      id: "TRX-8721",
      date: "Apr 29, 2025",
      source: "Trading Profit",
      type: "Daily Profit",
      amount: 25.5,
      status: "Completed",
      details: "2.5% ROI on 1,000 USDT",
    },
    {
      id: "TRX-8715",
      date: "Apr 28, 2025",
      source: "Alice Smith",
      type: "Referral Bonus",
      amount: 50.0,
      status: "Completed",
      details: "Level 1 Referral - 7% Commission",
    },
    {
      id: "TRX-8709",
      date: "Apr 27, 2025",
      source: "Trading Profit",
      type: "Daily Profit",
      amount: 18.25,
      status: "Completed",
      details: "1.8% ROI on 1,000 USDT",
    },
    {
      id: "TRX-8702",
      date: "Apr 25, 2025",
      source: "Bob Johnson",
      type: "Referral Bonus",
      amount: 50.0,
      status: "Completed",
      details: "Level 1 Referral - 7% Commission",
    },
    {
      id: "TRX-8698",
      date: "Apr 24, 2025",
      source: "Trading Profit",
      type: "Daily Profit",
      amount: 32.75,
      status: "Completed",
      details: "3.2% ROI on 1,000 USDT",
    },
    {
      id: "TRX-8691",
      date: "Apr 22, 2025",
      source: "Trading Profit",
      type: "Daily Profit",
      amount: 15.4,
      status: "Completed",
      details: "1.5% ROI on 1,000 USDT",
    },
    {
      id: "TRX-8685",
      date: "Apr 20, 2025",
      source: "Carol Davis",
      type: "Referral Bonus",
      amount: 50.0,
      status: "Completed",
      details: "Level 2 Referral - 3% Commission",
    },
    {
      id: "TRX-8679",
      date: "Apr 18, 2025",
      source: "Trading Profit",
      type: "Daily Profit",
      amount: 28.9,
      status: "Completed",
      details: "2.8% ROI on 1,000 USDT",
    },
    {
      id: "TRX-8672",
      date: "Apr 15, 2025",
      source: "Trading Profit",
      type: "Daily Profit",
      amount: 22.45,
      status: "Pending",
      details: "2.2% ROI on 1,000 USDT",
    },
    {
      id: "TRX-8665",
      date: "Apr 12, 2025",
      source: "David Wilson",
      type: "Referral Bonus",
      amount: 50.0,
      status: "Pending",
      details: "Level 1 Referral - 7% Commission",
    },
  ];
  const timeRangeOptions = [
    { value: "week", label: "This Week" },
    { value: "month", label: "This Month" },
    { value: "quarter", label: "This Quarter" },
    { value: "year", label: "This Year" },
    { value: "all", label: "All Time" },
  ];
  const chartPeriodOptions = [
    { value: "7d", label: "7D" },
    { value: "month", label: "1M" },
    { value: "3m", label: "3M" },
    { value: "year", label: "1Y" },
    { value: "all", label: "All" },
  ];
  const transactionTypeOptions = [
    { value: "all", label: "All Types" },
    { value: "commission", label: "Commission" },
    { value: "referral", label: "Referral Bonus" },
  ];
  const filteredTransactions = transactions
    .filter((transaction) => {
      const matchesSearch =
        transaction.source.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.amount.toString().includes(searchTerm);
      const matchesType =
        transactionType === "all" ||
        (transactionType === "commission" &&
          transaction.type === "Commission") ||
        (transactionType === "referral" &&
          transaction.type === "Referral Bonus");
      return matchesSearch && matchesType;
    })
    .sort((a, b) => {
      if (sortBy === "date") {
        return sortDirection === "asc"
          ? new Date(a.date).getTime() - new Date(b.date).getTime()
          : new Date(b.date).getTime() - new Date(a.date).getTime();
      } else if (sortBy === "amount") {
        return sortDirection === "asc"
          ? a.amount - b.amount
          : b.amount - a.amount;
      }
      return 0;
    });
  const itemsPerPage = 5;
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);
  const paginatedTransactions = filteredTransactions.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortDirection("asc");
    }
  };
  useEffect(() => {
    // Initialize earnings chart
    const earningsChartElement = document.getElementById("earnings-chart");
    if (earningsChartElement) {
      const earningsChart = echarts.init(earningsChartElement);
      const option = {
        animation: false,
        tooltip: {
          trigger: "axis",
          backgroundColor: "#1A1F2E",
          borderColor: "#374151",
          textStyle: {
            color: "#F3F4F6",
          },
          formatter: function (params: any) {
            return `${params[0].name}: $${params[0].value}`;
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
          ],
          axisLine: {
            lineStyle: {
              color: "#4B5563",
            },
          },
          axisLabel: {
            color: "#9CA3AF",
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: "#9CA3AF",
            formatter: function(value: any) {
              return `$${value}`;
            },
          },
          splitLine: {
            lineStyle: {
              color: "#374151",
            },
          },
        },
        series: [
          {
            name: "Total Earnings",
            data: [
              120, 210, 350, 480, 750, 890, 1050, 1250, 1450, 1650, 1750, 1875,
            ],
            type: "line",
            smooth: true,
            symbol: "circle",
            symbolSize: 8,
            itemStyle: {
              color: "#4f46e5",
            },
            lineStyle: {
              width: 3,
              color: "#4f46e5",
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(79, 70, 229, 0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(79, 70, 229, 0.1)",
                  },
                ],
              },
            },
          },
        ],
      };
      earningsChart.setOption(option);
      const handleResize = () => {
        earningsChart.resize();
      };
      window.addEventListener("resize", handleResize);
      return () => {
        window.removeEventListener("resize", handleResize);
        earningsChart.dispose();
      };
    }
    // Removed earnings breakdown chart initialization
  }, [selectedChartPeriod]);
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-white">Earnings Dashboard</h1>
      {/* Earnings Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Total Earnings Card */}
        <div className="bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <p className="text-sm text-gray-400 mb-1">Total Earnings</p>
                  <h2 className="text-3xl font-bold text-white">
                    ${earningsSummary.totalEarnings.toFixed(2)} USDT
                  </h2>
                  <p className="text-sm text-gray-500 mt-1">
                    ≈{" "}
                    {(
                      earningsSummary.totalEarnings * earningsSummary.usdtRate
                    ).toFixed(2)}{" "}
                    {earningsSummary.localCurrency}
                  </p>
                </div>
                <div className="bg-green-900 rounded-full p-2">
                  <DollarSign className="text-green-400" size={24} />
                </div>
              </div>
              <div className="flex items-center">
                <div
                  className={`flex items-center ${earningsSummary.totalEarningsChange >= 0 ? "text-green-400" : "text-red-400"}`}
                >
                  {earningsSummary.totalEarningsChange >= 0 ? (
                    <ArrowUp className="mr-1" size={14} />
                  ) : (
                    <ArrowDown className="mr-1" size={14} />
                  )}
                  <span className="text-sm font-medium">
                    {Math.abs(earningsSummary.totalEarningsChange)}%
                  </span>
                </div>
                <span className="text-xs text-gray-500 ml-2">
                  vs. last period
                </span>
              </div>
            </div>
            {/* Total Deposits Card */}
            <div className="bg-slate-800 rounded-lg shadow-md p-6 border border-slate-700">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <p className="text-sm text-gray-400 mb-1">Total Deposits</p>
                  <h2 className="text-3xl font-bold text-white">
                    ${earningsSummary.totalDeposits.toFixed(2)} USDT
                  </h2>
                  <p className="text-sm text-gray-500 mt-1">
                    ≈{" "}
                    {(
                      earningsSummary.totalDeposits * earningsSummary.usdtRate
                    ).toFixed(2)}{" "}
                    {earningsSummary.localCurrency}
                  </p>
                </div>
                <div className="bg-blue-900 rounded-full p-2">
                  <Wallet className="text-blue-400" size={24} />
                </div>
              </div>
              <div className="flex items-center">
                <div
                  className={`flex items-center ${earningsSummary.pendingEarningsChange >= 0 ? "text-green-400" : "text-red-400"}`}
                >
                  {earningsSummary.pendingEarningsChange >= 0 ? (
                    <ArrowUp className="mr-1" size={14} />
                  ) : (
                    <ArrowDown className="mr-1" size={14} />
                  )}
                  <span className="text-sm font-medium">
                    {Math.abs(earningsSummary.pendingEarningsChange)}%
                  </span>
                </div>
                <span className="text-xs text-gray-500 ml-2">
                  vs. last period
                </span>
              </div>
            </div>
            {/* Available Balance Card */}
            <div className="bg-slate-800 rounded-lg shadow-md p-6 border border-slate-700">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <p className="text-sm text-gray-400 mb-1">
                    Available Balance
                  </p>
                  <h2 className="text-3xl font-bold text-white">
                    {earningsSummary.availableBalance.toFixed(2)} USDT
                  </h2>
                  <p className="text-sm text-gray-500 mt-1">
                    ≈{" "}
                    {(
                      earningsSummary.availableBalance *
                      earningsSummary.usdtRate
                    ).toFixed(2)}{" "}
                    {earningsSummary.localCurrency}
                  </p>
                </div>
                <div className="bg-blue-900 rounded-full p-2">
                  <Wallet className="text-blue-400" size={24} />
                </div>
              </div>
              <div className="flex items-center">
                <div
                  className={`flex items-center ${earningsSummary.availableBalanceChange >= 0 ? "text-green-400" : "text-red-400"}`}
                >
                  {earningsSummary.availableBalanceChange >= 0 ? (
                    <ArrowUp className="mr-1" size={14} />
                  ) : (
                    <ArrowDown className="mr-1" size={14} />
                  )}
                  <span className="text-sm font-medium">
                    {Math.abs(earningsSummary.availableBalanceChange)}%
                  </span>
                </div>
                <span className="text-xs text-gray-500 ml-2">
                  vs. last period
                </span>
              </div>
            </div>
          </div>
          {/* Earnings Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Earnings Trend Chart */}
            <div className="bg-slate-800 rounded-lg shadow-md p-6 border border-slate-700 lg:col-span-2">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold text-white">
                  Earnings Trend
                </h2>
                <div className="flex space-x-2">
                  {chartPeriodOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => setSelectedChartPeriod(option.value)}
                      className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                        selectedChartPeriod === option.value
                          ? "bg-indigo-600 text-white"
                          : "text-gray-400 hover:bg-slate-700"
                      } cursor-pointer`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
              <div id="earnings-chart" className="h-80 w-full"></div>
            </div>
            {/* Earnings Breakdown Chart */}
            <div className="bg-slate-800 rounded-lg shadow-md p-6 border border-slate-700">
              <h2 className="text-lg font-semibold text-white mb-4">
                Earnings Breakdown
              </h2>
              <div className="space-y-6">
                <div className="bg-indigo-900/50 rounded-lg p-4 border border-indigo-800">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-indigo-300">
                      Daily Profit Total
                    </span>
                    <div className="bg-indigo-800 rounded-full p-2">
                      <DollarSign className="text-indigo-400" size={20} />
                    </div>
                  </div>
                  <div className="mb-1">
                    <span className="text-2xl font-bold text-white">
                      142.75 USDT
                    </span>
                  </div>
                  <div className="text-sm text-gray-500">≈ 142.75 USD</div>
                </div>
                <div className="bg-purple-900/50 rounded-lg p-4 border border-purple-800">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-purple-300">
                      Referral Bonus Total
                    </span>
                    <div className="bg-purple-800 rounded-full p-2">
                      <Users className="text-purple-400" size={20} />
                    </div>
                  </div>
                  <div className="mb-1">
                    <span className="text-2xl font-bold text-white">
                      250.00 USDT
                    </span>
                  </div>
                  <div className="text-sm text-gray-500">≈ 250.00 USD</div>
                </div>
              </div>
            </div>
          </div>
          {/* Transactions Section */}
          <div className="bg-slate-800 rounded-lg shadow-md p-6 border border-slate-700">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
              <h2 className="text-lg font-semibold text-white mb-4 md:mb-0">
                Transaction History
              </h2>
              <div className="flex flex-wrap gap-3">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search transactions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-slate-600 bg-slate-700 text-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 text-sm placeholder-gray-500"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="text-gray-400" size={16} />
                  </div>
                </div>
                <div className="relative">
                  <select
                    value={transactionType}
                    onChange={(e) => setTransactionType(e.target.value)}
                    className="pl-4 pr-10 py-2 border border-slate-600 bg-slate-700 text-gray-300 rounded-lg appearance-none focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                  >
                    {transactionTypeOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                    <ChevronDown className="text-gray-400" size={14} />
                  </div>
                </div>
                <div className="relative">
                  <button
                    onClick={() => setShowExportOptions(!showExportOptions)}
                    className="bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors flex items-center cursor-pointer"
                  >
                    <Download className="mr-2" size={16} /> Export
                  </button>
                  {showExportOptions && (
                    <div className="absolute right-0 mt-2 w-40 bg-slate-700 rounded-lg shadow-lg py-2 z-10 border border-slate-600">
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-slate-600 hover:text-white cursor-pointer">
                        <FileText className="mr-2 text-gray-400" size={16} />{" "}
                        CSV
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-slate-600 hover:text-white cursor-pointer">
                        <FileIcon className="mr-2 text-gray-400" size={16} />{" "}
                        PDF
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-slate-600 hover:text-white cursor-pointer">
                        <FileSpreadsheet
                          className="mr-2 text-gray-400"
                          size={16}
                        />{" "}
                        Excel
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
            {/* Filter Bar */}
            <div className="flex flex-wrap gap-4 mb-6 bg-slate-900 p-4 rounded-lg border border-slate-600">
              <div className="flex items-center">
                <span className="text-sm text-gray-400 mr-2">
                  Date Range:
                </span>
                <div className="flex items-center space-x-2">
                  <input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) =>
                      setDateRange({ ...dateRange, start: e.target.value })
                    }
                    className="px-3 py-1 border border-slate-600 bg-slate-700 text-gray-300 rounded-lg text-sm focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <span className="text-gray-500">to</span>
                  <input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) =>
                      setDateRange({ ...dateRange, end: e.target.value })
                    }
                    className="px-3 py-1 border border-slate-600 bg-slate-700 text-gray-300 rounded-lg text-sm focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>
              <div className="flex items-center">
                <span className="text-sm text-gray-400 mr-2">
                  Time Range:
                </span>
                <div className="relative">
                  <select
                    value={selectedTimeRange}
                    onChange={(e) => setSelectedTimeRange(e.target.value)}
                    className="pl-4 pr-10 py-1 border border-slate-600 bg-slate-700 text-gray-300 rounded-lg appearance-none focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                  >
                    {timeRangeOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                    <ChevronDown className="text-gray-400" size={14} />
                  </div>
                </div>
              </div>
              <button className="ml-auto bg-slate-700 hover:bg-slate-600 text-gray-300 px-3 py-1 rounded-lg text-sm transition-colors cursor-pointer">
                <RefreshCw className="mr-1" size={14} /> Reset Filters
              </button>
            </div>
            {/* Transactions Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-slate-700">
                <thead className="bg-slate-900">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Transaction ID
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort("date")}
                    >
                      <div className="flex items-center">
                        Date
                        {sortBy === "date" && (
                          <ArrowUpDown className="ml-1" size={14} />
                        )}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Source
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Type
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort("amount")}
                    >
                      <div className="flex items-center">
                        Amount
                        {sortBy === "amount" && (
                          <ArrowUpDown className="ml-1" size={14} />
                        )}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-slate-800 divide-y divide-slate-700">
                    {paginatedTransactions.length > 0 ? (
                      paginatedTransactions.map((transaction) => (
                        <tr key={transaction.id} className="hover:bg-slate-700">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                            {transaction.id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                            {transaction.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-8 w-8 rounded-full bg-indigo-800 flex items-center justify-center text-indigo-300 font-medium">
                                {transaction.source
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </div>
                              <div className="ml-3">
                                <div className="text-sm font-medium text-white">
                                  {transaction.source}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                            {transaction.type}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                            ${transaction.amount.toFixed(2)}
                          </td>
                          <td className="px-6 py-4">
                            <div>
                              <span
                                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  transaction.status === "Completed"
                                    ? "bg-green-900 text-green-300"
                                    : "bg-yellow-900 text-yellow-300"
                                }`}
                              >
                                {transaction.status}
                              </span>
                              <p className="text-xs text-gray-500 mt-1">
                                {transaction.details}
                              </p>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={6}
                          className="px-6 py-4 text-center text-sm text-gray-400"
                        >
                          No transactions found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-400">
                  Showing{" "}
                  <span className="font-medium">
                    {(currentPage - 1) * itemsPerPage + 1}
                  </span>{" "}
                  to{" "}
                  <span className="font-medium">
                    {Math.min(
                      currentPage * itemsPerPage,
                      filteredTransactions.length,
                    )}
                  </span>{" "}
                  of{" "}
                  <span className="font-medium">
                    {filteredTransactions.length}
                  </span>{" "}
                  results
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={currentPage === 1}
                    className={`px-3 py-1 rounded-md ${currentPage === 1 ? "bg-slate-700 text-gray-500 cursor-not-allowed" : "bg-slate-700 text-gray-300 hover:bg-slate-600 cursor-pointer"} border border-slate-600`}
                  >
                    Previous
                  </button>
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                    }
                    disabled={currentPage === totalPages}
                    className={`px-3 py-1 rounded-md ${currentPage === totalPages ? "bg-slate-700 text-gray-500 cursor-not-allowed" : "bg-slate-700 text-gray-300 hover:bg-slate-600 cursor-pointer"} border border-slate-600`}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
            </div>
            {/* Referral Program Promotion */}
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg shadow-md p-6 overflow-hidden relative">
              <div className="absolute right-0 top-0 h-full w-1/2 overflow-hidden">
                <img
                  src="https://readdy.ai/api/search-image?query=happy%20diverse%20business%20people%20celebrating%20success%2C%20financial%20growth%20chart%2C%20money%20falling%2C%20professional%20office%20setting%2C%20bright%20modern%20workspace%2C%20high%20quality%20professional%20photo%2C%20soft%20lighting%2C%20detailed%2C%20realistic&width=600&height=400&seq=1&orientation=landscape"
                  alt="Referral Success"
                  className="h-full w-full object-cover object-top"
                />
              </div>
              <div className="relative z-10 max-w-lg">
                <h2 className="text-2xl font-bold text-white mb-4">
                  Boost Your Earnings with Referrals
                </h2>
                <p className="text-indigo-100 mb-6">
                  Invite friends and earn up to 7% commission on their deposits.
                  Our multi-level referral program rewards you for building your
                  network.
                </p>
                <div className="flex space-x-4">
                  <button className="bg-white text-indigo-600 px-6 py-3 rounded-lg font-medium hover:bg-indigo-50 transition-colors cursor-pointer">
                    <UserPlus className="mr-2" size={16} /> Invite Friends
                  </button>
                  <a
                    href="https://readdy.ai/home/<USER>/a3a39fef-2598-4c0e-bd11-89f7851ad318"
                    data-readdy="true"
                    className="bg-indigo-800 bg-opacity-50 text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-70 transition-colors cursor-pointer"
                  >
                    <Users className="mr-2" size={16} /> View Referrals
                  </a>
                </div>
              </div>
            </div>
    </div>
  );
};
export default Earnings;
